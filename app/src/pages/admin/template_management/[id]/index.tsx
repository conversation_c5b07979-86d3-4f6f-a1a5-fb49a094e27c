import React, { useState } from 'react'
import { PrivateLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { KeyPairInterface } from "@src/redux/interfaces";
import { <PERSON><PERSON>, Card } from "react-bootstrap";
import { CustomEditor } from '@src/components/CustomEditor';



type TemplateManagementPageProps = {
  pageDetail: KeyPairInterface;
};

export default function TemplateManagementPage({
  pageDetail,
}: TemplateManagementPageProps) {
  const [state, setState] = useState({ content: "" });
  const [errors, setErrors] = useState({ content: "" });

  return (
    <section className="template">
      <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          <h1 className="m-0 page-head">
            {pageDetail?.title ?? "Template Management"}
          </h1>
          <h4 className="m-0 page-head primary-clr position-relative ps-3">
            Template
          </h4>
        </div>
      </div>

        <div className="box-content">
          <div className="row">
            <div className="col-lg-12">
              <Card>
                <Card.Body>
                  <div>
                    <span className='fw-bold pl-4'>Offer Letter Template</span>  
                  </div>
                  <div className="card-body">
                    <CustomEditor
                      value={state.content}
                      onChange={(content) => {
                        setState((prev: any) => ({ ...prev, content }));
                      }}
                    />
                    {errors?.content && (
                      <div className="text-danger mt-2">{errors?.content}</div>
                    )}
                  </div>

                </Card.Body>
                <Card.Footer>
                  <div className='d-flex gap-2'>
                    <Button className="btn btn-primary">
                      Preview
                    </Button>
                    <Button className="btn btn-primary">
                      Save
                    </Button>
                    <Button className="btn btn-danger border-0">
                      Delete
                    </Button>
                  </div>
                </Card.Footer>
              </Card>
            </div>
          </div>
        </div>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "template_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

TemplateManagementPage.layout = PrivateLayout;
