import React from 'react'
import { PrivateLayout } from "@src/components/Layout";
import {
  CandidatesRegisteredCard,
  StatsCard,
  HiringAnalyticsCard,
  LastestJobsPostedCard,
  TodayInterviewSchedule,
  JobStatsChartCard,
} from "@src/components/WildCard/AnalyticsManagement";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { KeyPairInterface } from "@src/redux/interfaces";
import { ExportAnalytics } from "@src/components/WildCard/AnalyticsManagement";

type TemplateManagementPageProps = {
  pageDetail: KeyPairInterface;
};

export default function TemplateManagementPage({
  pageDetail,
}: TemplateManagementPageProps) {
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");
  const interviewsPermissions =
    useEmployeeSelectedPagePermissions("interviews");

  return (
    <section className="hitemring">
      <PageHeader
        pageTitle={pageDetail?.title ?? "Analytics Management"}
        buttonComponent={<ExportAnalytics />}
        insideCard={false}>
        {/*Stats Card*/}
        <div className="card mb-3 Analytics-Management">
          <StatsCard />
        </div>

        {/* Hiring Analytics */}
        <div className="row row-gap-4">
          <div className="col-xl-7 col-12">
            <HiringAnalyticsCard />
          </div>
          <div className="col-xl-5 col-12">
            <CandidatesRegisteredCard />
          </div>

          {/*Lastest Job Posted Card*/}
          {jobPermissions && jobPermissions.includes("read") && (
            <div className="col-lg-12">
              <LastestJobsPostedCard settings={settings} />
            </div>
          )}

          {interviewsPermissions && interviewsPermissions.includes("read") ? (
            <>
              <div className="col-xl-5 col-12">
                <JobStatsChartCard />
              </div>
              {/* Today's Interview Schedule */}
              <div className="col-xl-7 col-12">
                <TodayInterviewSchedule />
              </div>
            </>
          ) : (
            <div className="col-lg-12 col-12">
              <JobStatsChartCard />
            </div>
          )}
        </div>
      </PageHeader>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "analytics_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

TemplateManagementPage.layout = PrivateLayout;
