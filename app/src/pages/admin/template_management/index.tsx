import React, { useState } from 'react'
import { PrivateLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { KeyPairInterface } from "@src/redux/interfaces";
import { Button } from "react-bootstrap";
import { Pagination } from "antd";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";

type TemplateManagementPageProps = {
  pageDetail: KeyPairInterface;
};

// Mock data for email templates
const emailTemplates = [
  {
    id: 1,
    name: "Welcome Email",
    subject: "Welcome to Our Company",
    type: "Welcome",
    status: "Active",
    created_at: "December 15, 2024 10:30 AM",
    created_by: "Admin User"
  },
  {
    id: 2,
    name: "Interview Invitation",
    subject: "Interview Invitation - {{position}}",
    type: "Interview",
    status: "Active",
    created_at: "December 14, 2024 02:15 PM",
    created_by: "HR Manager"
  },
  {
    id: 3,
    name: "Rejection Email",
    subject: "Application Status Update",
    type: "Rejection",
    status: "Inactive",
    created_at: "December 13, 2024 09:45 AM",
    created_by: "Admin User"
  }
];

// Mock data for offer letter templates
const offerLetterTemplates = [
  {
    id: 1,
    name: "Software Engineer Offer",
    position: "Software Engineer",
    department: "Engineering",
    status: "Active",
    created_at: "December 15, 2024 11:00 AM",
    created_by: "HR Manager"
  },
  {
    id: 2,
    name: "Marketing Manager Offer",
    position: "Marketing Manager",
    department: "Marketing",
    status: "Active",
    created_at: "December 12, 2024 03:30 PM",
    created_by: "Admin User"
  },
  {
    id: 3,
    name: "Sales Representative Offer",
    position: "Sales Representative",
    department: "Sales",
    status: "Inactive",
    created_at: "December 10, 2024 01:20 PM",
    created_by: "HR Manager"
  }
];

export default function TemplateManagementPage({
  pageDetail,
}: TemplateManagementPageProps) {
  const [activeTab, setActiveTab] = useState<'email' | 'offer'>('email');

  const handleEditTemplate = (templateId: number, type: 'email' | 'offer') => {
    console.log(`Edit ${type} template with ID: ${templateId}`);
    // TODO: Implement edit functionality
  };

  const handleDeleteTemplate = (templateId: number, type: 'email' | 'offer') => {
    console.log(`Delete ${type} template with ID: ${templateId}`);
    // TODO: Implement delete functionality
  };

  const handleViewTemplate = (templateId: number, type: 'email' | 'offer') => {
    console.log(`View ${type} template with ID: ${templateId}`);
    // TODO: Implement view functionality
  };

  const addNewEmailTemplateButton = (
    <Button className="btn btn-theme ms-auto">
      + Add Email Template
    </Button>
  );

  const addNewOfferLetterButton = (
    <Button className="btn btn-theme ms-auto">
      + Add Offer Letter Template
    </Button>
  );

  return (
    <section className="template">
      <PageHeader
        pageTitle={pageDetail?.title ?? "Template Management"}
        pageDescription={
          pageDetail?.description ?? "Manage email templates and offer letter templates"
        }
        buttonComponent={activeTab === 'email' ? addNewEmailTemplateButton : addNewOfferLetterButton}>

        <div className="box-content">
          {/* Tab Navigation */}
          <div className="mb-4">
            <nav className="nav nav-tabs">
              <button
                className={`nav-link ${activeTab === 'email' ? 'active' : ''}`}
                onClick={() => setActiveTab('email')}
                type="button">
                Email Templates
              </button>
              <button
                className={`nav-link ${activeTab === 'offer' ? 'active' : ''}`}
                onClick={() => setActiveTab('offer')}
                type="button">
                Offer Letter Templates
              </button>
            </nav>
          </div>

          {/* Email Templates Table */}
          {activeTab === 'email' && (
            <div className="tab-content">
              <div className="tab-pane fade show active">
                <div className={`table-responsive email-template-list ${emailTemplates.length > 0 ? "" : "no-records"}`}>
                  <table className="table table-hover dataTable" style={{ width: "100%" }}>
                    <thead>
                      <tr role="row">
                        <th className="mw-50px">Sr. No</th>
                        <th className="mw-150px">Template Name</th>
                        <th className="mw-200px">Subject</th>
                        <th className="mw-100px">Type</th>
                        <th className="mw-80px">Status</th>
                        <th className="mw-120px">Created By</th>
                        <th className="mw-120px">Date & Time</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {emailTemplates.length > 0 ? (
                        emailTemplates.map((template, index) => (
                          <tr key={template.id}>
                            <th>{index + 1}</th>
                            <td>
                              <a href="#" onClick={() => handleViewTemplate(template.id, 'email')}>
                                {template.name}
                              </a>
                            </td>
                            <td className="query-30-chars">{template.subject}</td>
                            <td>{template.type}</td>
                            <td>
                              <span className={`badge ${template.status === 'Active' ? 'bg-success' : 'bg-secondary'}`}>
                                {template.status}
                              </span>
                            </td>
                            <td>{template.created_by}</td>
                            <td>{template.created_at}</td>
                            <td>
                              <div className="template-action no-arrow">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    id={`email-template-dropdown-${template.id}`}
                                    className="text-decoration-none"
                                    as="span"
                                    type="button"
                                    role="button"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                    <span className="text-black small">
                                      <MoreVertIcon />
                                    </span>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu
                                    className="dropdown-menu-right shadow animated--grow-in"
                                    aria-labelledby={`email-template-dropdown-${template.id}`}>
                                    <Dropdown.Item
                                      onClick={() => handleViewTemplate(template.id, 'email')}
                                      className="no-decoration">
                                      View Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleEditTemplate(template.id, 'email')}
                                      className="no-decoration">
                                      Edit Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleDeleteTemplate(template.id, 'email')}
                                      className="no-decoration text-danger">
                                      Delete Template
                                    </Dropdown.Item>
                                  </Dropdown.Menu>
                                </Dropdown>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr className="no-records">
                          <td colSpan={8} className="text-center">
                            <span>No Email Templates Found</span>
                            <br />
                            {addNewEmailTemplateButton}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  className="mt-4"
                  current={1}
                  total={emailTemplates.length}
                  pageSize={10}
                  hideOnSinglePage
                  onChange={(page) => console.log('Email templates page:', page)}
                />
              </div>
            </div>
          )}

          {/* Offer Letter Templates Table */}
          {activeTab === 'offer' && (
            <div className="tab-content">
              <div className="tab-pane fade show active">
                <div className={`table-responsive offer-letter-list ${offerLetterTemplates.length > 0 ? "" : "no-records"}`}>
                  <table className="table table-hover dataTable" style={{ width: "100%" }}>
                    <thead>
                      <tr role="row">
                        <th className="mw-50px">Sr. No</th>
                        <th className="mw-150px">Template Name</th>
                        <th className="mw-150px">Position</th>
                        <th className="mw-120px">Department</th>
                        <th className="mw-80px">Status</th>
                        <th className="mw-120px">Created By</th>
                        <th className="mw-120px">Date & Time</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {offerLetterTemplates.length > 0 ? (
                        offerLetterTemplates.map((template, index) => (
                          <tr key={template.id}>
                            <th>{index + 1}</th>
                            <td>
                              <a href="#" onClick={() => handleViewTemplate(template.id, 'offer')}>
                                {template.name}
                              </a>
                            </td>
                            <td>{template.position}</td>
                            <td>{template.department}</td>
                            <td>
                              <span className={`badge ${template.status === 'Active' ? 'bg-success' : 'bg-secondary'}`}>
                                {template.status}
                              </span>
                            </td>
                            <td>{template.created_by}</td>
                            <td>{template.created_at}</td>
                            <td>
                              <div className="template-action no-arrow">
                                <Dropdown>
                                  <Dropdown.Toggle
                                    id={`offer-template-dropdown-${template.id}`}
                                    className="text-decoration-none"
                                    as="span"
                                    type="button"
                                    role="button"
                                    aria-haspopup="true"
                                    aria-expanded="false">
                                    <span className="text-black small">
                                      <MoreVertIcon />
                                    </span>
                                  </Dropdown.Toggle>
                                  <Dropdown.Menu
                                    className="dropdown-menu-right shadow animated--grow-in"
                                    aria-labelledby={`offer-template-dropdown-${template.id}`}>
                                    <Dropdown.Item
                                      onClick={() => handleViewTemplate(template.id, 'offer')}
                                      className="no-decoration">
                                      View Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleEditTemplate(template.id, 'offer')}
                                      className="no-decoration">
                                      Edit Template
                                    </Dropdown.Item>
                                    <Dropdown.Item
                                      onClick={() => handleDeleteTemplate(template.id, 'offer')}
                                      className="no-decoration text-danger">
                                      Delete Template
                                    </Dropdown.Item>
                                  </Dropdown.Menu>
                                </Dropdown>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr className="no-records">
                          <td colSpan={8} className="text-center">
                            <span>No Offer Letter Templates Found</span>
                            <br />
                            {addNewOfferLetterButton}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <Pagination
                  className="mt-4"
                  current={1}
                  total={offerLetterTemplates.length}
                  pageSize={10}
                  hideOnSinglePage
                  onChange={(page) => console.log('Offer letter templates page:', page)}
                />
              </div>
            </div>
          )}
        </div>
      </PageHeader>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "template_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

TemplateManagementPage.layout = PrivateLayout;
