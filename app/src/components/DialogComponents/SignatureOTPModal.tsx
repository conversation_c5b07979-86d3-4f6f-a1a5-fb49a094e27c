import React, { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { EmployeeInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { employeeManagementApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";
import {candidateApi} from "@src/apis/wildcardApis";
import { Button } from "antd";

// Props for the SignatureOTPModal component
type SignatureOTPModalProps = {
    candidateId: number;
    setIsVerifyied: (flag: boolean) => void;
    close: () => void;
};

// Default values for the OTP
const defaultValue: KeyPairInterface = {
  otp: "",
};

const DefaultOTPFields: GlobalInputFieldType[] = [
  {
    name: "otp",
    label: "OTP",
    type: "text",
    dataType: "onlynumber",
    minLength: 6,
    maxLength: 6,
    required: true,
  },
];

function SignatureOTPModal(props:SignatureOTPModalProps) {
    const {setIsVerifyied, close, candidateId} = props;
    const dispatch = useAppDispatch();

    // State to manage form data
    const [state, setState] = useState<KeyPairInterface>(defaultValue);

    // Function to handle form submission
    const onSubmit = async () => {
        await dispatch(setLoader(true));
        // Call API to create a new employee
        const { success, ...response } =
        await candidateApi.verifySignatureOTP(candidateId, state);
        await dispatch(setLoader(false));
        // Display flash message based on API response
        flashMessage(response.message, success ? "success" : "error");
        // If employee creation is successful, call the callback function to update the UI
        if (success) {
            setIsVerifyied(true);
            close();
        }
    };

    const handleResend = async () => {
        await dispatch(setLoader(true));
        const { success, ...response } =
        await candidateApi.sendSignatureOTPRequest(candidateId);
        await dispatch(setLoader(false));
        flashMessage(response.message, success ? "success" : "error");
    };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Submit" 
        fields={DefaultOTPFields} 
        setState={setState}
        state={state}
        onSubmit={onSubmit}
        onClose={close}
        customButtons={
          <>
            <Button
              className="ant-btn css-dev-only-do-not-override-n0gjrg ant-btn-default btn btn-theme mr-1 "
              onClick={handleResend}>
              Resend
            </button>
          </>
        }
      />
    </>
  )
}

export default SignatureOTPModal;