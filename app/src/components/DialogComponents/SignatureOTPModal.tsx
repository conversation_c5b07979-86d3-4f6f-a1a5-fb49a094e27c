import React, { useState, useEffect, useRef } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "../FlashMessage";
import { candidateApi } from "@src/apis/wildcardApis";
import { Button } from "antd";

// Props for the SignatureOTPModal component
type SignatureOTPModalProps = {
  candidateId: number;
  handleSignatureSave: () => void;
  close: () => void;
};

// Default values for the OTP
const defaultValue: KeyPairInterface = {
  otp: "",
};

const DefaultOTPFields: GlobalInputFieldType[] = [
  {
    name: "otp",
    label: "OTP",
    type: "text",
    dataType: "onlynumber",
    minLength: 6,
    maxLength: 6,
    required: true,
  },
];

function SignatureOTPModal(props: SignatureOTPModalProps) {
  const { close, candidateId, handleSignatureSave } = props;
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Timer state management
  const [timeLeft, setTimeLeft] = useState<number>(15 * 60); // 15 minutes in seconds
  const [canResend, setCanResend] = useState<boolean>(false);
  const [isTimerActive, setIsTimerActive] = useState<boolean>(true);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize timer on component mount
  useEffect(() => {
    startTimer();
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Start the countdown timer
  const startTimer = () => {
    setTimeLeft(15 * 60); // Reset to 15 minutes
    setCanResend(false);
    setIsTimerActive(true);

    timerRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          setCanResend(true);
          setIsTimerActive(false);
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);
  };

  // Format time for display (MM:SS)
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create a new employee
    const { success, ...response } = await candidateApi.verifySignatureOTP(
      candidateId,
      state,
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If employee creation is successful, call the callback function to update the UI
    if (success) {
      handleSignatureSave();
      close();
    }
  };

  const handleResend = async () => {
    if (!canResend) return;

    await dispatch(setLoader(true));
    const { success, ...response } =
      await candidateApi.sendSignatureOTPRequest(candidateId);
    await dispatch(setLoader(false));

    flashMessage(response.message, success ? "success" : "error");

    // If OTP was sent successfully, restart the timer
    if (success) {
      startTimer();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Submit"
        fields={DefaultOTPFields}
        setState={setState}
        state={state}
        onSubmit={onSubmit}
        onClose={close}
        customButtons={
          <>
            {isTimerActive ? (
              <div className="d-flex align-items-center me-3">
                <span className="text-muted small">
                  Resend OTP in:{" "}
                  <strong className="text-primary">
                    {formatTime(timeLeft)}
                  </strong>
                </span>
              </div>
            ) : (
              <Button
                className="ant-btn css-dev-only-do-not-override-n0gjrg ant-btn-default btn btn-theme mr-1"
                onClick={handleResend}
                disabled={!canResend}
                style={{
                  opacity: canResend ? 1 : 0.6,
                  cursor: canResend ? "pointer" : "not-allowed",
                }}>
                {canResend && "Resend OTP"}
              </Button>
            )}
          </>
        }
      />
    </>
  );
}

export default SignatureOTPModal;
