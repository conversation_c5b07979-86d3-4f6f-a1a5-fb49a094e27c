import ConfirmationModal from "./ConfirmationModal";
import ConfirmationFormModal from "./ConfirmationFormModal";
import NewBusinessModal from "./NewBusinessModal";
import EditBusinessModal from "./EditBusinessModal";
import SuccessModal from "./SuccessModal";
import VerifyBusinessEmail from "./VerifyBusinessEmail";

import NewDepartmentModal from "./NewDepartmentModal";
import EditDepartmentModal from "./EditDepartmentModal";
import ViewDepartmentModal from "./ViewDepartmentDetail";

import NewEmployeeModal from "./NewEmployeeModal";
import EditEmployeeModal from "./EditEmployeeModal";
import CandidateFiltersModal from "./CandidateFiltersModal";
import InterviewFiltersModal from "./InterviewFiltersModal";
import NewCandidateModal from "./NewCandidateModal";
import CustomEmailModal from "./CustomEmailModal";
import EditCandidateEducationModal from "./EditCandidateEducationModal";
import EditCandidateExperienceModal from "./EditCandidateExperienceModal";
import EditCandidateSkillModal from "./EditCandidateSkillModal";
import AddCandidateModal from "./AddCandidateModal";
import CandidateJobEmailModal from "./CandidateJobEmailModal";
import NewLocationModal from "./NewLocationModal";
import EditLocationModal from "./EditLocationModal";
import ViewLocationModal from "./ViewLocationModal";
import InterviewFeedbackModal from "./InterviewFeedbackModal";
import JobRequestModal from "./JobRequestModal";
import SaveCandidateRequestModal from "./SaveCandidateRequestModal";
import SendOfferLetterModal from "./SendOfferLetterModal";
import BusinessPaymentModal from "./BusinessPaymentModal";
import UpgradePlanModal from "./UpgradePlanModal";
import CandidateFeedbackModal from "./CandidateFeedbackModal";
import CandidateProfileCommentModal from "./CandidateProfileCommentModal";
import RequestDocumentModal from "./RequestDocumentModal";
import NewEmployeeRoleModal from "./NewEmployeeRoleModal";
import NewFieldModal from "./ExtraFieldModal";
import { FullscreenFlashMessage } from "../FlashMessage/FlashMessageComponent";

const SUCCESS_MODAL = {
  component: SuccessModal,
  title: "Success",
};

const ADD_NEW_BUSINESS = {
  component: NewBusinessModal,
  title: "Add New Business",
};

const EDIT_BUSINESS_MODAL = {
  component: EditBusinessModal,
  title: "Edit Business Detail",
};

const VERIFY_BUSINESS = {
  component: VerifyBusinessEmail,
  title: "Verify Business Email",
};

const CONFIRMATION_MODAL = {
  component: ConfirmationModal,
  title: "Confirmation Modal",
};

const CONFIRMATION_FORM_MODAL = {
  component: ConfirmationFormModal,
  title: "Confirmation Modal",
};

const ADD_NEW_DEPARTMENT = {
  component: NewDepartmentModal,
  title: "Add New Department",
};

const EDIT_DEPARTMENT_MODAL = {
  component: EditDepartmentModal,
  title: "Edit Department",
};

const VIEW_DEPARTMENT_MODAL = {
  component: ViewDepartmentModal,
  title: "Department Detail",
};

const ADD_NEW_EMPLOYEE = {
  component: NewEmployeeModal,
  title: "Add New Employee",
};

const EDIT_EMPLOYEE_MODAL = {
  component: EditEmployeeModal,
  title: "Edit Employee",
};

const NEW_CANDIDATE_MODAL = {
  component: NewCandidateModal,
  title: "Candidate Resume Upload",
};

const CANDIDATE_FILTER_MODAL = {
  component: CandidateFiltersModal,
  title: "Edit Opportunity Type",
  className: "antd-modal-right-side",
  styles: { body: { height: "100vh", overflowY: "auto", padding: 20 } },
};

const INTERVIEW_FILTER_MODAL = {
  component: InterviewFiltersModal,
  title: "Interview Filters",
  className: "antd-modal-right-side",
  styles: { body: { height: "100vh", overflowY: "auto", padding: 20 } },
};

const CUSTOM_EMAIL_MODAL = {
  component: CustomEmailModal,
  title: "Send Email",
  width: "50%",
};

const CANDIATE_JOB_EMAIL_MODAL = {
  component: CandidateJobEmailModal,
  title: "Send Email",
  width: "80%",
};

const ADD_CANDIDATE_MODAL = {
  component: AddCandidateModal,
  title: "Add Candidate",
};

const CANDIDATE_EDIT_EDUCATION_MODAL = {
  component: EditCandidateEducationModal,
  title: "Education Detail",
};

const CANDIDATE_EDIT_EXPERIENCE_MODAL = {
  component: EditCandidateExperienceModal,
  title: "Experience Detail",
  width: "60%",
};

const CANDIDATE_EDIT_SKILL_MODAL = {
  component: EditCandidateSkillModal,
  title: "Skill Detail",
};

const ADD_NEW_LOCATION = {
  component: NewLocationModal,
  title: "Add New location",
};

const EDIT_LOCATION_MODAL = {
  component: EditLocationModal,
  title: "Edit Location",
};

const VIEW_LOCATION_MODAL = {
  component: ViewLocationModal,
  title: "Location Detail",
};

const SAVE_INTERVIEW_FEEDBACK = {
  component: InterviewFeedbackModal,
  title: "Interview Feedback",
  width: "50%",
};

const JOB_REQUEST_MODAL = {
  component: JobRequestModal,
  title: "Apply Job",
  width: "50%",
};

const SAVE_CANDIDATE_REQUEST = {
  component: SaveCandidateRequestModal,
  title: "Save Candidate",
};

const SEND_OFFER_LETTER = {
  component: SendOfferLetterModal,
  title: "Send Offer Letter",
  width: "80%",
};

const BUSINESS_PAYMENT_MODAL = {
  component: BusinessPaymentModal,
  title: "Business Payment",
};

const UPGRADE_PLAN_MODAL = {
  component: UpgradePlanModal,
  title: "Upgrade Payment Plan",
};

const CANDIDATE_INTERVIEW_FEEDBACK_MODAL = {
  component: CandidateFeedbackModal,
  // title: "Interview Experience",
  width: "35%",
};

const CANDIDATE_PROFILE_COMMENT_MODAL = {
  component: CandidateProfileCommentModal,
  title: "Candidate Profile Comment",
  width: "50%",
};

const REQUEST_DOCUMENT_FORM_MODAL = {
  component: RequestDocumentModal,
  title: "Request Document",
};

const NEW_EMPLOYEE_ROLE_MODAL = {
  component: NewEmployeeRoleModal,
  title: "New Role",
};

const NEW_CUSTOM_FIELD_MODAL = {
  component: NewFieldModal,
  title: "New Field",
};

const INTERVIEW_WARNING_MESSAGE = {
  component: FullscreenFlashMessage,
  title: "",
  width: "40%",
  className: "interview-warning-modal",
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  SUCCESS_MODAL,
  ADD_NEW_BUSINESS,
  VERIFY_BUSINESS,
  CONFIRMATION_MODAL,
  CONFIRMATION_FORM_MODAL,
  ADD_NEW_DEPARTMENT,
  EDIT_DEPARTMENT_MODAL,
  VIEW_DEPARTMENT_MODAL,
  ADD_NEW_EMPLOYEE,
  EDIT_EMPLOYEE_MODAL,
  NEW_CANDIDATE_MODAL,
  INTERVIEW_FILTER_MODAL,
  CANDIDATE_FILTER_MODAL,
  CUSTOM_EMAIL_MODAL,
  CANDIDATE_EDIT_EDUCATION_MODAL,
  CANDIDATE_EDIT_EXPERIENCE_MODAL,
  CANDIDATE_EDIT_SKILL_MODAL,
  ADD_CANDIDATE_MODAL,
  CANDIATE_JOB_EMAIL_MODAL,
  ADD_NEW_LOCATION,
  EDIT_LOCATION_MODAL,
  VIEW_LOCATION_MODAL,
  SAVE_INTERVIEW_FEEDBACK,
  EDIT_BUSINESS_MODAL,
  JOB_REQUEST_MODAL,
  SAVE_CANDIDATE_REQUEST,
  SEND_OFFER_LETTER,
  BUSINESS_PAYMENT_MODAL,
  UPGRADE_PLAN_MODAL,
  CANDIDATE_INTERVIEW_FEEDBACK_MODAL,
  CANDIDATE_PROFILE_COMMENT_MODAL,
  REQUEST_DOCUMENT_FORM_MODAL,
  NEW_EMPLOYEE_ROLE_MODAL,
  NEW_CUSTOM_FIELD_MODAL,
  INTERVIEW_WARNING_MESSAGE,
};
