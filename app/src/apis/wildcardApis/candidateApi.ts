import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { CANDIDATE } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of candidates
const getCandidatesList = (params: any) => {
  return API.get(CANDIDATE, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get candidate detail
const getCandidateDetail = (id: number) => {
  return API.get(`${CANDIDATE}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create new candidate
const createCandidate = (data: any) => {
  return API.post(`${CANDIDATE}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update candidate detail
const updateCandidateDetail = (id: number, data: any) => {
  return API.put(`${CANDIDATE}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a candidates
const updateCandidateStatus = (id: number, data: any) => {
  return API.put(`${CANDIDATE}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get locations options of a candidates
const getCandidateLocationOptions = (data: any) => {
  return API.get(`${CANDIDATE}/options/locations`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get departments options of a candidates
const getCandidateStatusOptions = (data: any) => {
  return API.get(`${CANDIDATE}/options/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get qualifications options of a candidates
const getCandidateQualificationOptions = (data: any) => {
  return API.get(`${CANDIDATE}/options/qualifications`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get search filters of a candidates
const getCandidateSavedFilters = (params: any) => {
  return API.get(`${CANDIDATE}/search-filters`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to send document request email
const sendDocumentRequestEmail = (candidateId: number, body?: any) => {
  return API.post(`${CANDIDATE}/${candidateId}/request-document-email`, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to send document request email
const checkDocumentRequest = (candidateId: number, body?: any) => {
  return API.post(`${CANDIDATE}/${candidateId}/check-documents`, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to send document request email
const sendEmail = (candidateId: number, data: any) => {
  return API.post(`${CANDIDATE}/${candidateId}/send-email`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to delete a candidate
const deleteCandidate = (candidateId: number) => {
  return API.delete(`${CANDIDATE}/${candidateId}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Creates a blacklist candidate
const blacklistCandidate = (candidateId: number, data: any) => {
  return API.put(`${CANDIDATE}/${candidateId}/blacklist`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Creates a whitelist candidate
const whitelistCandidate = (candidateId: number) => {
  return API.put(`${CANDIDATE}/${candidateId}/whitelist`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Creates a schedule interview for a candidate.
const createScheduleCandidateInterview = (candidateId: number, data: any) => {
  return API.post(`${CANDIDATE}/${candidateId}/schedule-interview`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get feedbacks
const getFeedbacks = (candidateId: number, params: any) => {
  return API.get(`${CANDIDATE}/${candidateId}/feedback`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get documents
const getDocuments = (candidateId: number, params?: any) => {
  return API.get(`${CANDIDATE}/${candidateId}/documents`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get documents
const getReports = (params?: any) => {
  return API.get(`${CANDIDATE}/generate/reports`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get documents
const exportDetails = (params?: any) => {
  return API.get(`${CANDIDATE}/export/details`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get profile comments
const getProfileComments = (candidateId: number, params?: any) => {
  return API.get(`${CANDIDATE}/${candidateId}/comments`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to add comments on the profile
const addComment = (candidateId: number, params?: any) => {
  return API.post(`${CANDIDATE}/${candidateId}/add-comments`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getSimilarCandidate = (candidateId: number, params?: any) => {
  return API.get(`${CANDIDATE}/${candidateId}/similar_candidate`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const sendOfferLetter = (id: number, data: any) => {
  return API.post(`${CANDIDATE}/${id}/offer_letter/send`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const sendSignatureOTPRequest = (id: number) => {
  return API.post(`${CANDIDATE}/${id}/offer_letter/esign/otp/generate`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const verifySignatureOTP = (id: number, data: any) => {
  return API.post(`${CANDIDATE}/${id}/offer_letter/esign/otp/verify`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getRegisteredCandidate = (params?: any) => {
  return API.get(`${CANDIDATE}/registered/list`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getCandidatesList,
  updateCandidateStatus,
  updateCandidateDetail,
  createCandidate,
  getCandidateDetail,
  getCandidateLocationOptions,
  getCandidateStatusOptions,
  getCandidateQualificationOptions,
  getCandidateSavedFilters,
  sendDocumentRequestEmail,
  checkDocumentRequest,
  deleteCandidate,
  createScheduleCandidateInterview,
  sendEmail,
  getFeedbacks,
  getDocuments,
  getReports,
  blacklistCandidate,
  whitelistCandidate,
  exportDetails,
  getProfileComments,
  addComment,
  getSimilarCandidate,
  sendOfferLetter,
  getRegisteredCandidate,
  sendSignatureOTPRequest,
  verifySignatureOTP,
};
